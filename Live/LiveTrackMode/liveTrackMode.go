package livetrackmode

import (
	"log"
	"sync"

	communication "oscbridge/Communication"
	live "oscbridge/Live"
	// Assurez-vous que le package types est correctement importé
)

// Note: Toutes les constantes OSC sont maintenant définies dans liveTrack_types.go

// LiveTrackMode gère le mode Track de Live
type LiveTrackMode struct {
	*communication.BaseMode
	trackManager      *live.LiveTrackManager
	commManager       *communication.CommunicationManager
	displayManager    *LiveTrackDisplayManager
	selectedTrackData *TrackData
	lockedTrackData   *TrackData
	lockedTrackIndex  *int
	isActive          bool
	currentPage       int
	isLocked          bool
	isQuickView       bool

	// Gestion des handlers (maps initialisées dans NewLiveTrackMode)
	backgroundHandlers map[string]func([]interface{})
	activeHandlers     map[string]func([]interface{})
	hardwareHandler    func(communication.HardwareEvent)

	// Mutex pour protéger l'accès concurrentiel aux données partagées
	mutex sync.RWMutex
}

// NewLiveTrackMode crée une nouvelle instance du mode Track
func NewLiveTrackMode(trackManager *live.LiveTrackManager, commManager *communication.CommunicationManager) *LiveTrackMode {
	mode := &LiveTrackMode{
		BaseMode:          communication.NewBaseMode(),
		trackManager:      trackManager,
		commManager:       commManager,
		selectedTrackData: DefaultTrackData(), // Initialiser avec les valeurs par défaut
		lockedTrackData:   nil,                // Initialisé à nil par défaut
		lockedTrackIndex:  nil,                // Initialisé à nil par défaut
		isActive:          false,
		currentPage:       1,     // Valeur par défaut pour currentPage
		isLocked:          false, // Valeur par défaut pour isLocked
		isQuickView:       false, // Valeur par défaut pour isQuickView

		// Initialisation des maps de handlers
		backgroundHandlers: make(map[string]func([]interface{})),
		activeHandlers:     make(map[string]func([]interface{})),
		hardwareHandler:    nil, // Initialisé à nil
	}

	// Initialiser le displayManager
	mode.displayManager = NewLiveTrackDisplayManager(commManager, mode)

	// Enregistrer les écouteurs d'événements background ici si nécessaire,
	// ou dans Initialize comme dans liveVolumeMode.go
	// mode.registerEventHandlers()

	return mode
}

// Initialize initialise le mode Track
func (m *LiveTrackMode) Initialize(service communication.OscService) {
	log.Println("Initialisation du mode Track...")

	// Initialiser le mode de base
	log.Println("LiveTrackMode.Initialize: Appel à BaseMode.Initialize")
	m.BaseMode.Initialize(service)

	// Vérifier l'initialisation des structures de données critiques
	if m.backgroundHandlers == nil {
		log.Println("LiveTrackMode.Initialize: backgroundHandlers est nil, initialisation d'une nouvelle map")
		m.backgroundHandlers = make(map[string]func([]interface{}))
	}

	if m.displayManager == nil {
		log.Println("LiveTrackMode.Initialize: displayManager est nil, tentative de réinitialisation")
		m.displayManager = NewLiveTrackDisplayManager(m.commManager, m)
	}

	// Enregistrer les handlers OSC background
	log.Println("LiveTrackMode.Initialize: Appel à registerOSCHandlers")
	m.registerOSCHandlers() // Cette méthode sera définie plus tard

	// Enregistrer les écouteurs d'événements background
	log.Println("LiveTrackMode.Initialize: Appel à registerEventHandlers")
	m.registerEventHandlers() // Cette méthode sera définie plus tard

	// Envoyer le message initial pour mettre à jour le mode Track (adresse à confirmer)
	log.Println("LiveTrackMode.Initialize: Envoi du message initial pour start_listen_trackMode")
	err := m.BaseMode.Send(OscAddressStartListenTrackMode, []interface{}{})
	if err != nil {
		log.Printf("LiveTrackMode.Initialize: Erreur lors de l'envoi du message initial: %v", err)
	}

	// Initialiser l'affichage (via le displayManager qui sera initialisé plus tard)
	// m.displayManager.ClearEmptySlots()

	log.Println("Mode Track initialisé avec succès.")
}

// Activate active le mode Track et démarre les listeners actifs
func (m *LiveTrackMode) Activate() {
	log.Println("\n=== Activating TrackMode ===")

	// Utiliser l'état QuickView déjà présent dans le state
	m.mutex.Lock()
	isQuickView := m.isQuickView
	isLocked := m.isLocked
	m.isActive = true

	// Vérification des structures de données
	if m.selectedTrackData == nil {
		m.selectedTrackData = DefaultTrackData()
		m.selectedTrackData.TrackIdx = 0
		m.selectedTrackData.DisplayIndex = "0"
		m.selectedTrackData.FormattedTrackNumber = "1"
	}

	if m.displayManager == nil {
		m.displayManager = NewLiveTrackDisplayManager(m.commManager, m)
	}
	m.mutex.Unlock()

	// Vérifier que commManager est initialisé
	if m.commManager == nil {
		log.Println("Activate: commManager est nil, impossble d'activer le mode Track")
		return
	}

	// Envoyer message de mode
	m.commManager.SendMessage("mo,0", true)

	// Mise à jour de l'affichage avec l'état combiné
	m.displayManager.UpdateTrackViewState(isQuickView, isLocked)

	m.BaseMode.Send(OscAddressSongStartListenPosition)

	m.mutex.RLock()
	lockedData := m.lockedTrackData
	m.mutex.RUnlock()

	// Rafraîchir l'affichage si nécessaire
	if !isQuickView && isLocked && lockedData != nil {
		// Si mode normal et lock actif, rafraîchir l'affichage avec la lockedTrack
		var returnNames []string
		if m.trackManager != nil {
			returnNames = m.trackManager.GetReturnTracksName()
		}
		m.displayManager.RefreshFullTrackUIDisplay(lockedData, m.currentPage, m.IsActive(), returnNames)
	}

	// Activer le mode de base
	m.BaseMode.Activate()

	// Configurer le handler hardware
	m.hardwareHandler = m.HandleHardwareEvent
	if m.commManager.GetHardwareManager() != nil {
		m.commManager.AddHardwareEventHandler(m.hardwareHandler)
	}

	// Activer l'affichage du mode
	if m.displayManager != nil {
		m.displayManager.UpdateModeDisplay("0")
	}

	log.Println("=== TrackMode Activation Complete ===")
}

// Deactivate désactive le mode Track (utilisé lors du changement de mode)
func (m *LiveTrackMode) Deactivate() {
	log.Println("\n=== Deactivating TrackMode ===")

	m.mutex.Lock()
	wasQuickView := m.isQuickView
	wasLocked := m.isLocked
	selectedData := m.selectedTrackData
	currentPage := m.currentPage
	m.isActive = false
	m.mutex.Unlock()

	// Si on sort d'un mode quickview false avec lock, il faut remettre l'affichage sur la selected track
	if !wasQuickView && wasLocked && selectedData != nil && m.displayManager != nil {
		var returnNames []string
		if m.trackManager != nil {
			returnNames = m.trackManager.GetReturnTracksName()
		}
		m.displayManager.RefreshFullTrackUIDisplay(selectedData, currentPage, false, returnNames)
	}

	m.BaseMode.Deactivate()

	m.BaseMode.Send(OscAddressSongStopListenPosition)

	// Supprimer uniquement les handlers actifs
	if m.hardwareHandler != nil {
		m.commManager.RemoveHardwareEventHandler(m.hardwareHandler)
		m.hardwareHandler = nil
	}

	// Ne pas effacer l'affichage du mode Track ici
	// car cela peut interférer avec l'activation du mode suivant
	// Nous laissons le mode suivant gérer son propre affichage

	log.Println("=== TrackMode Deactivation Complete ===")
}

// CleanupForExit nettoie toutes les ressources avant la sortie de l'application
func (m *LiveTrackMode) CleanupForExit() {
	log.Println("\n=== TrackMode Complete Cleanup Start ===")

	// Désactiver d'abord le mode s'il est actif
	if m.IsActive() {
		m.Deactivate()
	}

	// Supprimer les handlers background OSC
	m.mutex.Lock() // Utiliser Lock car on modifie la map
	for address := range m.backgroundHandlers {
		m.BaseMode.UnregisterHandler(address)
		delete(m.backgroundHandlers, address)
		log.Printf("Unregistered OSC handler for: %s", address)
	}
	m.mutex.Unlock()

	// Supprimer les écouteurs d'événements du trackManager
	if m.trackManager != nil {
		// Note : Assurez-vous que les noms d'événements correspondent à ceux utilisés dans registerEventHandlers
		m.trackManager.RemoveListener("selectedTrackDeviceUpdate", m.onSelectedTrackChange)
		m.trackManager.RemoveListener("returnTracksNameChange", m.onReturnTracksNameChange)
		log.Println("Removed listeners for trackManager events: selectedTrackDeviceUpdate, returnTracksNameChange")
	} else {
		log.Println("CleanupForExit: trackManager is nil, cannot remove listeners.")
	}

	log.Println("=== TrackMode Complete Cleanup Complete ===")
}

// IsActive retourne si le mode est actif
func (m *LiveTrackMode) IsActive() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.isActive
}

// GetLockState retourne l'état de verrouillage actuel du mode de manière thread-safe.
func (m *LiveTrackMode) GetLockState() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.isLocked
}

// GetQuickViewState retourne l'état QuickView actuel du mode de manière thread-safe.
func (m *LiveTrackMode) GetQuickViewState() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.isQuickView
}

// shouldUpdateUI détermine si l'interface utilisateur doit être mise à jour pour un paramètre reçu.
// trackIndex: l'index de la piste pour laquelle le paramètre a été reçu via OSC.
// isSelectedTrackParamURL: true si l'adresse OSC d'origine du paramètre était pour la selectedTrack
// (par exemple, /live/trackMode/get/volume), false si c'était pour la lockedTrack
// (par exemple, /live/trackLockMode/get/volume).
func (m *LiveTrackMode) shouldUpdateUI(trackIndex int, isSelectedTrackParamURL bool) bool {
	m.mutex.RLock()
	isActive := m.isActive
	isQuickView := m.isQuickView
	isLocked := m.isLocked

	currentSelectedTrackIdx := -1
	if m.selectedTrackData != nil {
		currentSelectedTrackIdx = m.selectedTrackData.TrackIdx
	}

	currentLockedTrackIdx := -1
	if m.lockedTrackIndex != nil {
		currentLockedTrackIdx = *m.lockedTrackIndex
	}
	m.mutex.RUnlock()

	// Condition d'exclusion SPÉCIFIQUE pour les mises à jour de selectedTrackData:
	// Si le mode Track est ACTIF, qu'il N'EST PAS en QuickView, ET qu'il EST Verrouillé.
	// Dans ce cas, les changements de selectedTrackData (qui n'est pas affichée) ne doivent PAS impacter l'UI.
	excludeDisplayUpdateForSelectedTrack := isActive && !isQuickView && isLocked

	if isSelectedTrackParamURL {
		// Le paramètre provient d'une URL pour selectedTrack.
		if currentSelectedTrackIdx != trackIndex {
			// log.Printf("shouldUpdateUI: Paramètre pour selectedTrack (URL) mais l'index OSC %d ne correspond pas à selectedTrackData.TrackIdx %d. Pas d'update UI.", trackIndex, currentSelectedTrackIdx)
			return false // Le message OSC ne concerne pas la piste actuellement stockée comme selectedTrackData.
		}

		// Mettre à jour l'UI SAUF si la condition d'exclusion est remplie.
		if excludeDisplayUpdateForSelectedTrack {
			// log.Printf("shouldUpdateUI: Paramètre pour selectedTrack (URL), index %d. Condition d'exclusion (actif, non-quickview, locked) remplie. Pas d'update UI.", trackIndex)
			return false
		}
		// log.Printf("shouldUpdateUI: Paramètre pour selectedTrack (URL), index %d. Update UI autorisée.", trackIndex)
		return true // Mettre à jour l'UI dans tous les autres cas pour selectedTrack.

	} else {
		// Le paramètre provient d'une URL pour lockedTrack.
		if currentLockedTrackIdx != trackIndex {
			// log.Printf("shouldUpdateUI: Paramètre pour lockedTrack (URL) mais l'index OSC %d ne correspond pas à lockedTrackData.TrackIdx %d. Pas d'update UI.", trackIndex, currentLockedTrackIdx)
			return false // Le message OSC ne concerne pas la piste actuellement stockée comme lockedTrackData.
		}

		// Si le paramètre concerne la lockedTrack (identifiée par l'URL), on met à jour l'UI
		// SEULEMENT si le mode Track est ACTIF (car c'est la lockedTrack qui est alors affichée).
		// if !isActive {
		// 	log.Printf("shouldUpdateUI: Paramètre pour lockedTrack (URL), index %d. Mode Track INACTIF. Pas d'update UI.", trackIndex)
		// } else {
		// 	log.Printf("shouldUpdateUI: Paramètre pour lockedTrack (URL), index %d. Mode Track ACTIF. Update UI autorisée.", trackIndex)
		// }
		return isActive
	}
}

// --- Méthodes clés (signatures seulement pour l'instant) ---

// registerEventHandlers enregistre les écouteurs d'événements pour le LiveTrackManager et CommunicationManager
func (m *LiveTrackMode) registerEventHandlers() {
	log.Println("LiveTrackMode: Registering event handlers...")
	if m.trackManager != nil {
		// Enregistrer les listeners pour les changements importants du trackManager
		m.trackManager.On("selectedTrackDeviceUpdate", m.onSelectedTrackChange)
		m.trackManager.On("returnTracksNameChange", m.onReturnTracksNameChange)
		log.Println("Registered listeners for trackManager events: selectedTrackDeviceUpdate, returnTracksNameChange")
	} else {
		log.Println("LiveTrackMode.registerEventHandlers: trackManager is nil, cannot register listeners.")
	}

	// Note: Les listeners pour les événements hardware (encoderChange, buttonPressed, touchPressed)
	// sont gérés directement par le hardwareHandler ajouté dans Activate() et retiré dans Deactivate(),
	// pas besoin de les enregistrer/désenregistrer explicitement ici.
}

// HandleOscMessage gère les messages OSC reçus
func (m *LiveTrackMode) HandleOscMessage(args []interface{}, address string) {
	// Trouver le handler enregistré pour cette adresse
	m.mutex.RLock()
	handler, exists := m.backgroundHandlers[address]
	m.mutex.RUnlock() // Utiliser RLock car on ne modifie pas la map ici

	if exists {
		//log.Printf("LiveTrackMode.HandleOscMessage: Routing message for %s", address)
		handler(args) // Exécuter le handler correspondant
	} else {
		log.Printf("LiveTrackMode.HandleOscMessage: No handler found for address %s", address)
	}
}

// SetPage définit la page actuelle
// SetPage définit la page actuelle pour les sends/returns et met à jour l'affichage
// La logique réelle est maintenant dans liveTrack_hardwareHandlers.go (ou reste ici si non liée au hardware?)
func (m *LiveTrackMode) SetPage(page int) {
	log.Printf("LiveTrackMode.SetPage: Setting page to %d", page)

	m.mutex.Lock()
	m.currentPage = page
	m.mutex.Unlock()

	// Obtenir les données de la piste actuellement affichée
	m.mutex.RLock()
	var currentTrack *TrackData
	if m.isLocked && m.lockedTrackData != nil {
		currentTrack = m.lockedTrackData
	} else if !m.isLocked && m.selectedTrackData != nil {
		currentTrack = m.selectedTrackData
	}
	m.mutex.RUnlock()

	// Mettre à jour l'affichage des sends et des noms de retours pour la nouvelle page
	if currentTrack != nil && currentTrack.Sends != nil {
		m.displayManager.UpdateSendsDisplay(currentTrack.Sends, page, SendsPerPage)
	} else {
		// Si pas de piste ou pas de sends, effacer l'affichage des sends
		// Le displayManager.UpdateSendsDisplay gère déjà le cas où sends est vide,
		// mais on peut aussi appeler ClearSlot pour les sends si nécessaire.
		// Pour l'instant, faisons confiance à UpdateSendsDisplay.
	}

	// Obtenir les noms des pistes de retour depuis le trackManager
	var returnNames []string
	if m.trackManager != nil {
		// Cette méthode peut avoir un nom différent selon l'implémentation réelle
		returnNames = m.trackManager.GetReturnTracksName()
		log.Printf("updateReturnNamesForCurrentPage: Retrieved %d return track names", len(returnNames))
	} else {
		log.Println("updateReturnNamesForCurrentPage: trackManager is nil, cannot get return names")
		returnNames = []string{} // Initialiser avec un slice vide en cas d'erreur
	}

	// Appel à la fonction pour mettre à jour les noms de retour
	m.updateReturnNamesForCurrentPage()
}

// onSelectedTrackChange réagit quand la piste sélectionnée change
func (m *LiveTrackMode) onSelectedTrackChange(args []interface{}) {
	log.Printf("LiveTrackMode.onSelectedTrackChange: Received args %v", args)

	if len(args) == 0 {
		log.Println("LiveTrackMode.onSelectedTrackChange: No arguments received")
		return
	}

	// Le premier argument est l'index de la piste sélectionnée (*int)
	selectedTrackIndexPtr, ok := args[0].(*int)
	if !ok || selectedTrackIndexPtr == nil {
		log.Printf("LiveTrackMode.onSelectedTrackChange: Unexpected argument type or nil pointer: %T", args[0])
		return
	}
	selectedTrackIndex := *selectedTrackIndexPtr

	log.Printf("LiveTrackMode.onSelectedTrackChange: New selected track index: %d", selectedTrackIndex)

	// Mettre à jour l'état interne pour savoir quelle est la piste sélectionnée
	m.mutex.Lock()
	// Si la piste sélectionnée change, réinitialiser les données actuelles ?
	// Ou attendre les nouvelles données OSC ? Pour l'instant, on ne réinitialise pas
	// selectedTrackData sera mis à jour par les messages /live/trackMode/get/*
	// On pourrait stocker l'index attendu si besoin
	m.mutex.Unlock()

	// Si le mode n'est pas verrouillé, l'affichage sera mis à jour par HandleParameterUpdate
	m.mutex.RLock()
	isLocked := m.isLocked
	m.mutex.RUnlock()

	if !isLocked {
		log.Println("LiveTrackMode.onSelectedTrackChange: Not locked. Processing track", selectedTrackIndex)
		// Note: Les return names seront mis à jour automatiquement quand les données
		// de la piste arriveront via handleOscInitialTrackData, pas besoin de les envoyer ici
		// pour éviter les doublons
	} else {
		log.Println("LiveTrackMode.onSelectedTrackChange: Mode is locked, ignoring selection change for UI update.")
	}
}

// onReturnTracksNameChange réagit quand les noms des pistes de retour changent
func (m *LiveTrackMode) onReturnTracksNameChange(args []interface{}) {
	log.Printf("LiveTrackMode.onReturnTracksNameChange: Received args %v", args)

	if len(args) == 0 {
		log.Println("LiveTrackMode.onReturnTracksNameChange: No arguments received")
		return
	}

	// Supposons que le premier argument est une liste de noms de pistes de retour ([]string)
	// Cette partie dépend du format de l'événement émis par LiveTrackManager
	returnNames, ok := args[0].([]string) // Assurez-vous que le type correspond
	if !ok {
		log.Printf("LiveTrackMode.onReturnTracksNameChange: Unexpected argument type: %T", args[0])
		return
	}

	log.Printf("LiveTrackMode.onReturnTracksNameChange: Return track names updated: %v", returnNames)

	// Mettre à jour l'affichage des noms de retours via le displayManager

	m.displayManager.UpdateReturnNames(returnNames, m.currentPage, SendsPerPage)
}

// Note: Les fonctions parseOscInt, parseOscFloat, parseOscBool, parseOscString
// sont actuellement définies dans liveTrack_oscHandlers.go.
// Pour les utiliser ici, il faudrait soit :
// 1. Les rendre exportables (commençant par une majuscule) dans liveTrack_oscHandlers.go
// 2. Les redéfinir ici dans liveTrackMode.go
// 3. Créer un package utils/osc pour ces fonctions.
// L'option 1 ou 3 est préférable pour la réutilisation.
// Pour l'instant, je vais les copier ici pour que le code compile,
// mais il faudrait refactoriser cela plus tard.

// Copie des fonctions de parsing OSC (à refactoriser)

// getTargetTrackData retourne les données de la piste cible (sélectionnée ou verrouillée)
func (m *LiveTrackMode) getTargetTrackData(trackIndex int) *TrackData {
	log.Printf("LiveTrackMode.getTargetTrackData called for track index %d (not implemented yet)", trackIndex)
	// Implémentation future: retourner les données de la piste appropriée
	return nil // Placeholder
}

// updateReturnNamesForCurrentPage met à jour l'affichage des noms des pistes de retour pour la page actuelle
func (m *LiveTrackMode) updateReturnNamesForCurrentPage() {
	// Obtenir le numéro de page actuel
	m.mutex.RLock()
	currentPage := m.currentPage
	m.mutex.RUnlock()

	// Obtenir les noms des pistes de retour depuis le trackManager
	var returnNames []string
	if m.trackManager != nil {
		// Cette méthode peut avoir un nom différent selon l'implémentation réelle
		returnNames = m.trackManager.GetReturnTracksName()
		log.Printf("updateReturnNamesForCurrentPage: Retrieved %d return track names", len(returnNames))
	} else {
		log.Println("updateReturnNamesForCurrentPage: trackManager is nil, cannot get return names")
		returnNames = []string{} // Initialiser avec un slice vide en cas d'erreur
	}

	// Mettre à jour l'affichage des noms de retour
	m.displayManager.UpdateReturnNames(returnNames, currentPage, SendsPerPage)
}

// SetQuickViewState définit directement l'état QuickView du mode
func (m *LiveTrackMode) SetQuickViewState(isQuickView bool) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	log.Printf("LiveTrackMode.SetQuickViewState: Définition de l'état QuickView: %v", isQuickView)
	m.isQuickView = isQuickView
}

// InitializeMode initialise le mode avec un état QuickView spécifique (si implémenté)
func (m *LiveTrackMode) InitializeMode(isQuickView bool) {
	log.Printf("LiveTrackMode.InitializeMode: Initialisation avec QuickView: %v", isQuickView)
	m.SetQuickViewState(isQuickView)
}
